<template>
	<view class="container">
		<view class="decoration"></view>
		
		<!-- 顶部标题 -->
		<view class="header">
			<view class="back-button" @click="goBack">←</view>
			<text class="header-title">我的预约</text>
		</view>

		<view class="main-content">
			<!-- 标签切换 -->
			<view class="tab-container">
				<view 
					class="tab" 
					:class="{ active: activeTab === item.value }"
					v-for="(item, index) in tabs" 
					:key="index"
					@tap="switchTab(item.value)"
				>
					{{ item.label }}
				</view>
			</view>

			<!-- 预约列表 -->
			<scroll-view 
				v-if="appointmentList.length > 0" 
				class="appointment-scroll" 
				:scroll-y="true" 
				:scroll-top="scrollTop"
				@scrolltolower="loadMore"
			>
				<view class="appointment-card" v-for="(appointment, index) in appointmentList" :key="index">
					<view class="appointment-header">
						<text class="doctor-info">{{ appointment.doctorName }} - {{ appointment.department }}</text>
						<view class="appointment-status" :class="getStatusClass(appointment.status)">
							{{ getStatusText(appointment.status) }}
						</view>
					</view>
					<view class="appointment-info">
						<view class="info-item">
							<text class="info-label">预约时间：</text>
							<text>{{ appointment.appointmentTime }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">咨询内容：</text>
							<text>{{ appointment.consultContent }}</text>
						</view>
					</view>
					<view class="appointment-actions">
						<view class="action-button" @tap="cancelAppointment(appointment)" v-if="appointment.status === 'pending'">
							取消预约
						</view>
						<view class="action-button primary" @tap="contactDoctor(appointment)">
							联系医生
						</view>
					</view>
				</view>
				
				<view class="load-more" v-if="hasMore">
					<text>{{ loadingText }}</text>
				</view>
			</scroll-view>
			
			<tui-show-empty v-else text="暂无预约记录"></tui-show-empty>
		</view>
	</view>
</template>

<script>
	const util = require("@/utils/util.js");
	const api = require('@/utils/api.js');
	
	export default {
		data() {
			return {
				activeTab: 'pending',
				tabs: [
					{ label: '待确认', value: 'pending' },
					{ label: '已确认', value: 'confirmed' },
					{ label: '已完成', value: 'completed' }
				],
				appointmentList: [],
				page: 1,
				size: 10,
				hasMore: true,
				scrollTop: 0,
				loadingText: '加载中...'
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 切换标签
			switchTab(tab) {
				this.activeTab = tab;
				this.page = 1;
				this.hasMore = true;
				this.appointmentList = [];
				this.getAppointmentList();
			},
			
			// 获取预约列表
			getAppointmentList() {
				let that = this;
				
				// 显示加载提示
				if (that.page === 1) {
					uni.showLoading({
						title: '加载中...'
					});
				}
				
				// 这里使用模拟数据，实际项目中应该调用真实API
				// util.request(api.AppointmentList, {
				// 	page: that.page,
				// 	size: that.size,
				// 	status: that.activeTab
				// }).then(function(res) {
				// 	if (res.errno === 0) {
				// 		if (that.page === 1) {
				// 			that.appointmentList = res.data.datalist;
				// 		} else {
				// 			that.appointmentList = that.appointmentList.concat(res.data.datalist);
				// 		}
				// 		that.hasMore = res.data.hasMore;
				// 	}
				// 	uni.hideLoading();
				// });
				
				// 模拟数据
				setTimeout(() => {
					const mockData = that.getMockAppointments();
					if (that.page === 1) {
						that.appointmentList = mockData;
					} else {
						that.appointmentList = that.appointmentList.concat(mockData);
					}
					that.hasMore = that.page < 3; // 模拟只有3页数据
					uni.hideLoading();
				}, 1000);
			},
			
			// 加载更多
			loadMore() {
				if (this.hasMore) {
					this.page++;
					this.loadingText = '加载更多...';
					this.getAppointmentList();
				}
			},
			
			// 取消预约
			cancelAppointment(appointment) {
				uni.showModal({
					title: '确认取消',
					content: '确定要取消这个预约吗？',
					success: (res) => {
						if (res.confirm) {
							// 实际项目中应该调用API
							uni.showToast({
								title: '取消成功',
								icon: 'success'
							});
							// 刷新列表
							this.switchTab(this.activeTab);
						}
					}
				});
			},
			
			// 联系医生
			contactDoctor(appointment) {
				// 跳转到聊天页面或拨打电话
				uni.showActionSheet({
					itemList: ['拨打电话', '在线咨询'],
					success: (res) => {
						if (res.tapIndex === 0) {
							// 拨打电话
							uni.makePhoneCall({
								phoneNumber: appointment.doctorPhone || '************'
							});
						} else if (res.tapIndex === 1) {
							// 在线咨询
							uni.navigateTo({
								url: `/pages/chat/chat?doctorId=${appointment.doctorId}`
							});
						}
					}
				});
			},
			
			// 获取状态样式类
			getStatusClass(status) {
				switch(status) {
					case 'pending':
						return 'status-pending';
					case 'confirmed':
						return 'status-confirmed';
					case 'completed':
						return 'status-completed';
					default:
						return '';
				}
			},
			
			// 获取状态文本
			getStatusText(status) {
				switch(status) {
					case 'pending':
						return '待确认';
					case 'confirmed':
						return '已确认';
					case 'completed':
						return '已完成';
					default:
						return '未知';
				}
			},
			
			// 获取模拟数据
			getMockAppointments() {
				const appointments = {
					pending: [
						{
							id: 1,
							doctorName: '张医生',
							department: '中医内科',
							appointmentTime: '2024-03-20 14:30',
							consultContent: '脾胃调理、亚健康调理',
							status: 'pending',
							doctorId: 'doctor_001',
							doctorPhone: '************'
						}
					],
					confirmed: [
						{
							id: 2,
							doctorName: '李医生',
							department: '针灸科',
							appointmentTime: '2024-03-22 10:00',
							consultContent: '颈椎病调理、经络疏通',
							status: 'confirmed',
							doctorId: 'doctor_002',
							doctorPhone: '************'
						}
					],
					completed: [
						{
							id: 3,
							doctorName: '王医生',
							department: '中医内科',
							appointmentTime: '2024-03-18 09:00',
							consultContent: '失眠调理、心理疏导',
							status: 'completed',
							doctorId: 'doctor_003',
							doctorPhone: '************'
						}
					]
				};
				
				return appointments[this.activeTab] || [];
			}
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.page = 1;
			this.hasMore = true;
			this.appointmentList = [];
			this.getAppointmentList();
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		
		onLoad: function(options) {
			this.getAppointmentList();
		}
	}
</script>

<style lang="scss">
	:root {
		--primary-color: #8B4513;
		--secondary-color: #D2B48C;
		--background-color: #FDF5E6;
		--text-color: #4A4A4A;
		--accent-color: #CD853F;
	}

	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	.container {
		max-width: 750rpx;
		margin: 0 auto;
		background: #fff;
		min-height: 100vh;
		position: relative;
	}

	.decoration {
		position: absolute;
		width: 100%;
		height: 100%;
		pointer-events: none;
		opacity: 0.05;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/><path d="M25,25 L75,25 L75,75 L25,75 Z" fill="%238B4513"/><path d="M37.5,37.5 L62.5,37.5 L62.5,62.5 L37.5,62.5 Z" fill="%238B4513"/></svg>');
		background-size: 100rpx 100rpx;
	}

	.header {
		background: var(--primary-color);
		color: #fff;
		padding: 30rpx;
		text-align: center;
		position: relative;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23D2B48C" stroke-width="2"/><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="none" stroke="%23D2B48C" stroke-width="1"/><path d="M40,40 L60,40 L60,60 L40,60 Z" fill="none" stroke="%23D2B48C" stroke-width="0.5"/></svg>');
		background-size: 40rpx 40rpx;
		border-bottom: 4rpx solid var(--secondary-color);
	}

	.header-title {
		font-size: 48rpx;
		letter-spacing: 8rpx;
		text-shadow: 4rpx 4rpx 8rpx rgba(0,0,0,0.2);
		font-weight: bold;
	}

	.back-button {
		position: absolute;
		left: 30rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #fff;
		font-size: 40rpx;
		font-weight: bold;
	}

	.main-content {
		padding: 40rpx;
		margin-bottom: 120rpx;
	}

	.tab-container {
		display: flex;
		border-bottom: 4rpx solid var(--secondary-color);
		margin-bottom: 40rpx;
		background: #fff;
		position: sticky;
		top: 0;
		z-index: 100;
	}

	.tab {
		flex: 1;
		text-align: center;
		padding: 24rpx;
		color: var(--text-color);
		transition: all 0.3s ease;
		position: relative;
		font-size: 32rpx;
	}

	.tab.active {
		color: var(--primary-color);
		font-weight: bold;
	}

	.tab.active::after {
		content: '';
		position: absolute;
		bottom: -4rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background: var(--primary-color);
	}

	.appointment-scroll {
		height: calc(100vh - 400rpx);
	}

	.appointment-card {
		background: #fff;
		border: 4rpx solid var(--secondary-color);
		border-radius: 16rpx;
		padding: 40rpx;
		margin-bottom: 40rpx;
		position: relative;
		overflow: hidden;
	}

	.appointment-card::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 8rpx;
		background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
	}

	.appointment-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.doctor-info {
		font-size: 36rpx;
		color: var(--primary-color);
		font-weight: bold;
	}

	.appointment-status {
		padding: 8rpx 24rpx;
		border-radius: 30rpx;
		font-size: 28rpx;
		background: var(--background-color);
	}

	.status-pending {
		color: #E6A23C;
		background: #FDF6EC;
	}

	.status-confirmed {
		color: #67C23A;
		background: #F0F9EB;
	}

	.status-completed {
		color: #909399;
		background: #F4F4F5;
	}

	.appointment-info {
		margin-bottom: 30rpx;
	}

	.info-item {
		display: flex;
		margin-bottom: 16rpx;
		color: var(--text-color);
		font-size: 30rpx;
	}

	.info-label {
		width: 160rpx;
		color: #666;
	}

	.appointment-actions {
		display: flex;
		justify-content: flex-end;
		gap: 20rpx;
		border-top: 2rpx solid var(--secondary-color);
		padding-top: 30rpx;
	}

	.action-button {
		padding: 16rpx 32rpx;
		border: 2rpx solid var(--secondary-color);
		border-radius: 8rpx;
		color: var(--text-color);
		transition: all 0.3s ease;
		font-size: 28rpx;
	}

	.action-button.primary {
		background: var(--primary-color);
		color: #fff;
		border-color: var(--primary-color);
	}

	.action-button:active {
		background: var(--background-color);
	}

	.action-button.primary:active {
		background: var(--accent-color);
	}

	.load-more {
		text-align: center;
		padding: 40rpx;
		color: #666;
		font-size: 28rpx;
	}
</style>
